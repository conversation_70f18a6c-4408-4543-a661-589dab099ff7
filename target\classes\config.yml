# Custom Recipes Configuration
# 自定义合成配方配置文件

# 配方列表
recipes:
  # 生命果实配方示例
  life_fruit:
    # 配方名称
    name: "生命果实"
    # 结果物品
    result:
      material: GOLDEN_APPLE
      amount: 1
      data: 1  # 1表示附魔金苹果
      # 自定义显示名称
      display_name: "&6&l生命果实"
      # 自定义lore描述
      lore:
        - "&7一颗蕴含着强大生命力的神秘果实"
        - "&7食用后可以获得强大的生命恢复能力"
        - ""
        - "&e&l稀有度: &6传说"
        - "&e&l效果: &a生命恢复 + 伤害吸收"
    # 合成配方 (3x3网格)
    # 使用字母代表不同材料，空格或'AIR'表示空位
    shape:
      - "DDD"
      - "DGD"
      - "EEE"
    # 材料映射
    ingredients:
      D: DIAMOND
      E: EMERALD
      G: GOLDEN_APPLE
    # 是否有序合成 (true=有序, false=无序)
    shaped: true

  # 你可以添加更多自定义配方
  # 示例：钻石剑配方
  diamond_sword_custom:
    name: "自定义钻石剑"
    result:
      material: DIAMOND_SWORD
      amount: 1
    shape:
      - " D "
      - " D "
      - " S "
    ingredients:
      D: DIAMOND
      S: STICK
    shaped: true

  # 无序合成示例
  golden_apple_simple:
    name: "简单金苹果"
    result:
      material: GOLDEN_APPLE
      amount: 1
      data: 0
    # 无序合成只需要列出所需材料和数量
    ingredients:
      GOLD_INGOT: 8
      APPLE: 1
    shaped: false

# 插件设置
settings:
  # 是否启用插件
  enabled: true
  # 是否在玩家加入时显示欢迎消息
  show_welcome: true
  # 欢迎消息
  welcome_message: "&a[CustomRecipes] &7自定义合成配方已加载！"
  # 是否覆盖原版配方
  override_vanilla: false
