package com.customrecipes;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;

/**
 * 配置管理器
 * Configuration Manager
 */
public class ConfigManager {
    
    private final CustomRecipePlugin plugin;
    private FileConfiguration config;
    
    public ConfigManager(CustomRecipePlugin plugin) {
        this.plugin = plugin;
        this.config = plugin.getConfig();
    }
    
    /**
     * 重新加载配置
     */
    public void reloadConfig() {
        plugin.reloadConfig();
        this.config = plugin.getConfig();
    }
    
    /**
     * 插件是否启用
     */
    public boolean isEnabled() {
        return config.getBoolean("settings.enabled", true);
    }
    
    /**
     * 是否显示欢迎消息
     */
    public boolean shouldShowWelcome() {
        return config.getBoolean("settings.show_welcome", true);
    }
    
    /**
     * 获取欢迎消息
     */
    public String getWelcomeMessage() {
        return config.getString("settings.welcome_message", "&a[CustomRecipes] &7自定义合成配方已加载！");
    }
    
    /**
     * 是否覆盖原版配方
     */
    public boolean shouldOverrideVanilla() {
        return config.getBoolean("settings.override_vanilla", false);
    }
    
    /**
     * 获取配方配置节
     */
    public ConfigurationSection getRecipesSection() {
        return config.getConfigurationSection("recipes");
    }
    
    /**
     * 获取配置文件
     */
    public FileConfiguration getConfig() {
        return config;
    }
}
