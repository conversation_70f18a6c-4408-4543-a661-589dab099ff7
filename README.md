# CustomRecipes - 自定义合成配方插件

一个为Minecraft 1.8.8设计的自定义工作台合成配方插件。

## 功能特性

- ✅ 支持自定义工作台合成配方
- ✅ 支持有序合成（Shaped Recipe）
- ✅ 支持无序合成（Shapeless Recipe）
- ✅ 完全兼容原版工作台合成机制
- ✅ 可配置的配方文件
- ✅ 热重载配置功能
- ✅ 包含附魔金苹果配方示例

## 安装方法

1. 将编译好的 `CustomRecipes.jar` 文件放入服务器的 `plugins` 文件夹
2. 重启服务器或使用 `/reload` 命令
3. 插件会自动生成 `config.yml` 配置文件

## 编译方法

确保你已安装Java 8和Maven，然后运行：

```bash
mvn clean package
```

编译完成后，在 `target` 文件夹中找到 `CustomRecipes-1.0.0.jar` 文件。

## 配置说明

### 基本配置

```yaml
settings:
  enabled: true              # 是否启用插件
  show_welcome: true         # 是否显示欢迎消息
  welcome_message: "&a[CustomRecipes] &7自定义合成配方已加载！"
  override_vanilla: false    # 是否覆盖原版配方
```

### 配方配置

#### 有序合成配方（Shaped Recipe）

```yaml
recipes:
  enchanted_golden_apple:
    name: "附魔金苹果"
    result:
      material: GOLDEN_APPLE
      amount: 1
      data: 1
    shape:
      - "GGG"
      - "GAG" 
      - "GGG"
    ingredients:
      G: GOLD_BLOCK
      A: APPLE
    shaped: true
```

#### 无序合成配方（Shapeless Recipe）

```yaml
recipes:
  golden_apple_simple:
    name: "简单金苹果"
    result:
      material: GOLDEN_APPLE
      amount: 1
      data: 0
    ingredients:
      GOLD_INGOT: 8
      APPLE: 1
    shaped: false
```

### 配方参数说明

- `name`: 配方显示名称
- `result`: 合成结果
  - `material`: 物品材料类型
  - `amount`: 数量
  - `data`: 数据值（可选，用于区分物品变种）
- `shape`: 合成形状（仅有序合成需要）
- `ingredients`: 材料映射
- `shaped`: 是否为有序合成

## 命令

- `/customrecipes` 或 `/cr` - 显示帮助信息
- `/customrecipes reload` - 重新加载配方配置

## 权限

- `customrecipes.admin` - 管理员权限（默认：OP）

## 示例配方

插件默认包含以下示例配方：

1. **附魔金苹果** - 使用8个金块和1个苹果合成
2. **自定义钻石剑** - 标准钻石剑配方
3. **简单金苹果** - 使用8个金锭和1个苹果的无序合成

## 支持的材料

插件支持所有Minecraft 1.8.8的原版材料。材料名称请参考Bukkit Material枚举。

常用材料示例：
- `DIAMOND`, `GOLD_INGOT`, `IRON_INGOT`
- `APPLE`, `GOLDEN_APPLE`
- `STICK`, `STONE`, `COBBLESTONE`
- `GOLD_BLOCK`, `DIAMOND_BLOCK`, `IRON_BLOCK`

## 注意事项

1. 配方修改后需要使用 `/customrecipes reload` 命令重新加载
2. 确保材料名称正确，错误的材料名称会导致配方加载失败
3. 有序合成的形状必须是3x3网格内的有效排列
4. 插件与原版合成系统完全兼容

## 技术支持

如果遇到问题，请检查：
1. 服务器控制台的错误信息
2. 配置文件语法是否正确
3. 材料名称是否有效
4. 服务器版本是否为1.8.8

## 版本信息

- 插件版本：1.0.0
- 支持版本：Minecraft 1.8.8
- API版本：Bukkit/Spigot 1.8.8
