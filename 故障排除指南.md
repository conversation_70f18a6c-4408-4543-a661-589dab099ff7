# 🔧 CustomRecipes 故障排除指南

## 问题：工作台物品闪烁，物品栏出现重复物品

### 🔍 问题描述
- 点击合成结果后，工作台中的物品会闪烁
- 物品栏中出现大量重复物品
- 合成的物品会消失

### 🎯 解决方案

#### 方案1：使用新的配方排列
我已经修改了配方，避免与原版配方冲突：

**新配方：**
```
[💎] [💎] [💎]
[💎] [🍎] [💎]  
[💚] [💚] [💚]
```

这个配方更独特，不会与任何原版配方冲突。

#### 方案2：检查服务器设置

1. **确保服务器版本正确**
   - 必须是 Minecraft 1.8.8
   - 使用 Spigot 或 Bukkit

2. **检查插件加载**
   ```
   /plugins
   ```
   确保 CustomRecipes 显示为绿色

3. **重新加载配方**
   ```
   /customrecipes reload
   ```

#### 方案3：清理冲突

1. **停止服务器**
2. **删除旧配方**
   - 删除 `plugins/CustomRecipes/` 文件夹
3. **重新启动服务器**
4. **测试新配方**

### 📋 测试步骤

1. **准备材料**
   - 5个钻石
   - 3个绿宝石
   - 1个金苹果

2. **按照新配方摆放**
   ```
   [💎] [💎] [💎]
   [💎] [🍎] [💎]  
   [💚] [💚] [💚]
   ```

3. **检查结果**
   - 应该显示带有"生命果实"名字的附魔金苹果
   - 点击后应该正常合成

### 🐛 如果问题仍然存在

#### 检查控制台日志
查看服务器控制台是否有错误信息：
```
[CustomRecipes] 已加载配方: 生命果实
[CustomRecipes] 配方 life_fruit 设置材料: D = DIAMOND
[CustomRecipes] 配方 life_fruit 设置材料: E = EMERALD
[CustomRecipes] 配方 life_fruit 设置材料: G = GOLDEN_APPLE
```

#### 尝试简化配方
如果问题持续，可以尝试无序合成：

```yaml
life_fruit:
  name: "生命果实"
  result:
    material: GOLDEN_APPLE
    amount: 1
    data: 1
    display_name: "&6&l生命果实"
    lore:
      - "&7一颗神秘的生命果实"
  ingredients:
    DIAMOND: 5
    EMERALD: 3
    GOLDEN_APPLE: 1
  shaped: false
```

#### 禁用其他插件
暂时禁用其他可能冲突的插件：
- 其他合成相关插件
- 物品修改插件
- 经济插件

### 📞 获取帮助

如果问题仍未解决，请提供：
1. 服务器版本信息
2. 控制台错误日志
3. 其他已安装的插件列表
4. 配置文件内容

### 🎯 预防措施

1. **定期备份配置**
2. **测试新配方前先备份世界**
3. **避免与原版配方相似的设计**
4. **使用独特的材料组合**

---

**记住：新配方现在是上面3行钻石，中间金苹果，下面3行绿宝石！**
