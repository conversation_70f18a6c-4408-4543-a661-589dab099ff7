package com.customrecipes;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.plugin.java.JavaPlugin;

/**
 * 自定义合成配方插件主类
 * Custom Recipe Plugin Main Class for Minecraft 1.8.8
 */
public class CustomRecipePlugin extends JavaPlugin implements Listener {

    private RecipeManager recipeManager;
    private ConfigManager configManager;

    @Override
    public void onEnable() {
        // 保存默认配置文件
        saveDefaultConfig();

        // 初始化管理器
        configManager = new ConfigManager(this);
        recipeManager = new RecipeManager(this);

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(this, this);

        // 加载配方
        loadRecipes();

        getLogger().info("CustomRecipes 插件已启用！");
        getLogger().info("已加载 " + recipeManager.getRecipeCount() + " 个自定义配方");
    }

    @Override
    public void onDisable() {
        // 移除所有自定义配方
        if (recipeManager != null) {
            recipeManager.removeAllRecipes();
        }
        getLogger().info("CustomRecipes 插件已禁用！");
    }

    /**
     * 加载配方
     */
    public void loadRecipes() {
        if (!configManager.isEnabled()) {
            getLogger().info("插件已在配置中禁用");
            return;
        }

        // 重新加载配置
        reloadConfig();
        configManager.reloadConfig();

        // 移除旧配方并加载新配方
        recipeManager.removeAllRecipes();
        recipeManager.loadRecipesFromConfig();
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("customrecipes")) {
            if (!sender.hasPermission("customrecipes.admin")) {
                sender.sendMessage(ChatColor.RED + "你没有权限使用此命令！");
                return true;
            }

            if (args.length == 0) {
                sender.sendMessage(ChatColor.GOLD + "=== CustomRecipes 帮助 ===");
                sender.sendMessage(ChatColor.YELLOW + "/customrecipes reload - 重新加载配方");
                sender.sendMessage(ChatColor.YELLOW + "当前已加载 " + recipeManager.getRecipeCount() + " 个配方");
                return true;
            }

            if (args[0].equalsIgnoreCase("reload")) {
                loadRecipes();
                sender.sendMessage(ChatColor.GREEN + "配方已重新加载！共加载 " + recipeManager.getRecipeCount() + " 个配方");
                return true;
            }
        }
        return false;
    }

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        if (configManager.shouldShowWelcome()) {
            Player player = event.getPlayer();
            String message = configManager.getWelcomeMessage();
            player.sendMessage(ChatColor.translateAlternateColorCodes('&', message));
        }
    }

    public RecipeManager getRecipeManager() {
        return recipeManager;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }
}
