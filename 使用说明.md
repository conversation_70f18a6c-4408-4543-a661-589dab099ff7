# CustomRecipes 插件使用说明

## 🎯 插件简介

CustomRecipes 是一个专为 Minecraft 1.8.8 设计的自定义工作台合成配方插件。它允许服务器管理员通过配置文件轻松添加、修改和管理自定义合成配方。

## 📦 安装步骤

1. **下载插件**
   - 编译后的插件文件：`target/CustomRecipes-1.0.0.jar`

2. **安装插件**
   - 将 `CustomRecipes-1.0.0.jar` 复制到服务器的 `plugins` 文件夹
   - 重启服务器或使用 `/reload` 命令

3. **验证安装**
   - 服务器启动后，检查控制台是否显示插件加载信息
   - 使用命令 `/customrecipes` 测试插件是否正常工作

## ⚙️ 配置文件详解

插件首次运行时会在 `plugins/CustomRecipes/` 目录下生成 `config.yml` 配置文件。

### 基础设置

```yaml
settings:
  enabled: true              # 是否启用插件
  show_welcome: true         # 玩家加入时是否显示欢迎消息
  welcome_message: "&a[CustomRecipes] &7自定义合成配方已加载！"
  override_vanilla: false    # 是否覆盖原版配方（谨慎使用）
```

### 配方配置

#### 附魔金苹果配方示例

```yaml
recipes:
  enchanted_golden_apple:
    name: "附魔金苹果"
    result:
      material: GOLDEN_APPLE
      amount: 1
      data: 1  # 1表示附魔金苹果，0表示普通金苹果
    shape:
      - "GGG"
      - "GAG" 
      - "GGG"
    ingredients:
      G: GOLD_BLOCK
      A: APPLE
    shaped: true
```

这个配方的含义：
- 使用 8 个金块 + 1 个苹果
- 按照 3x3 网格排列（金块围绕苹果）
- 合成出 1 个附魔金苹果

## 🔧 配方类型

### 1. 有序合成（Shaped Recipe）

有序合成要求材料按特定位置摆放：

```yaml
diamond_sword_custom:
  name: "自定义钻石剑"
  result:
    material: DIAMOND_SWORD
    amount: 1
  shape:
    - " D "
    - " D "
    - " S "
  ingredients:
    D: DIAMOND
    S: STICK
  shaped: true
```

### 2. 无序合成（Shapeless Recipe）

无序合成只需要正确的材料数量，不要求位置：

```yaml
golden_apple_simple:
  name: "简单金苹果"
  result:
    material: GOLDEN_APPLE
    amount: 1
    data: 0
  ingredients:
    GOLD_INGOT: 8
    APPLE: 1
  shaped: false
```

## 🎮 游戏内命令

| 命令 | 权限 | 说明 |
|------|------|------|
| `/customrecipes` | `customrecipes.admin` | 显示插件帮助信息 |
| `/customrecipes reload` | `customrecipes.admin` | 重新加载配方配置 |
| `/cr` | `customrecipes.admin` | `/customrecipes` 的简写 |

## 🔑 权限系统

- `customrecipes.admin` - 管理员权限（默认：OP）
  - 允许使用所有插件命令
  - 允许重新加载配方配置

## 📝 添加自定义配方

### 步骤 1：编辑配置文件

打开 `plugins/CustomRecipes/config.yml`，在 `recipes:` 节点下添加新配方：

```yaml
recipes:
  # 现有配方...
  
  # 你的新配方
  my_custom_recipe:
    name: "我的自定义物品"
    result:
      material: DIAMOND
      amount: 2
    shape:
      - "III"
      - "IGI"
      - "III"
    ingredients:
      I: IRON_INGOT
      G: GOLD_INGOT
    shaped: true
```

### 步骤 2：重新加载配方

使用命令 `/customrecipes reload` 重新加载配方，无需重启服务器。

## 🧪 常用材料列表

| 材料名称 | 说明 |
|----------|------|
| `DIAMOND` | 钻石 |
| `GOLD_INGOT` | 金锭 |
| `IRON_INGOT` | 铁锭 |
| `GOLD_BLOCK` | 金块 |
| `DIAMOND_BLOCK` | 钻石块 |
| `APPLE` | 苹果 |
| `GOLDEN_APPLE` | 金苹果 |
| `STICK` | 木棍 |
| `STONE` | 石头 |
| `COBBLESTONE` | 圆石 |

完整材料列表请参考 Bukkit Material 枚举。

## ⚠️ 注意事项

1. **配方冲突**：避免创建与原版配方完全相同的自定义配方
2. **材料名称**：确保使用正确的材料名称，错误的名称会导致配方加载失败
3. **形状限制**：有序合成的形状必须在 3x3 网格内
4. **数据值**：使用 `data` 字段区分物品变种（如普通金苹果 data:0，附魔金苹果 data:1）
5. **重新加载**：修改配置后必须使用 `/customrecipes reload` 命令

## 🐛 故障排除

### 配方不生效
1. 检查控制台是否有错误信息
2. 验证配置文件语法是否正确
3. 确认材料名称是否有效
4. 使用 `/customrecipes reload` 重新加载

### 插件无法加载
1. 确认服务器版本为 1.8.8
2. 检查插件文件是否完整
3. 查看控制台错误信息

### 配方冲突
1. 检查是否与原版配方冲突
2. 确认配方的唯一性
3. 调整配方设计

## 📞 技术支持

如果遇到问题，请提供以下信息：
- 服务器版本
- 插件版本
- 错误信息截图
- 配置文件内容

---

**祝你使用愉快！** 🎉
