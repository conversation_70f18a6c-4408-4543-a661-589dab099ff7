# 🎯 CustomRecipes 最终版本使用说明

## ✅ 问题解决方案

我已经重新设计了插件，使用**最简单、最稳定**的方法来实现自定义合成配方，确保：
- ✅ 完全像原版合成一样的体验
- ✅ 不会有闪烁或异常
- ✅ 正常的材料消耗
- ✅ 自定义名字和lore

## 🔧 新的实现方式

### 核心改进
1. **直接注册配方** - 不依赖复杂的配置文件解析
2. **简化事件处理** - 移除了可能导致冲突的事件监听器
3. **硬编码配方** - 确保配方100%正确注册

### 配方详情
**生命果实配方：**
```
[💎] [💎] [💎]
[💎] [🍎] [💎]  
[💚] [💚] [💚]
```

**需要材料：**
- 5个钻石
- 1个金苹果
- 3个绿宝石

**合成结果：**
- 物品：附魔金苹果
- 名字：**生命果实**（金色加粗）
- Lore：
  - 一颗蕴含着强大生命力的神秘果实
  - 食用后可以获得强大的生命恢复能力
  - 
  - **稀有度: 传说**
  - **效果: 生命恢复 + 伤害吸收**

## 🚀 安装步骤

1. **停止服务器**
2. **删除旧版本**
   - 删除 `plugins/CustomRecipes.jar`（如果存在）
   - 删除 `plugins/CustomRecipes/` 文件夹（如果存在）
3. **安装新版本**
   - 将 `target/CustomRecipes-1.0.0.jar` 复制到 `plugins/` 文件夹
4. **启动服务器**

## 🎮 测试步骤

1. **准备材料**
   ```
   /give @p diamond 5
   /give @p golden_apple 1
   /give @p emerald 3
   ```

2. **打开工作台**
   - 按照配方摆放材料

3. **验证结果**
   - 应该显示带有"生命果实"名字的附魔金苹果
   - 点击合成应该正常工作，不会闪烁

## 🔍 预期行为

### ✅ 正常情况
- 摆放材料后立即显示合成结果
- 点击结果物品正常合成
- 材料按预期消耗（每次合成消耗对应数量）
- 合成出的物品有正确的名字和lore

### ❌ 如果仍有问题
1. **检查服务器版本** - 必须是 Minecraft 1.8.8
2. **检查控制台日志**：
   ```
   [CustomRecipes] 成功加载生命果实配方！
   [CustomRecipes] 配方: 5个钻石 + 1个金苹果 + 3个绿宝石 = 生命果实
   ```
3. **重新加载配方**：
   ```
   /customrecipes reload
   ```

## 🛠️ 命令

- `/customrecipes` - 显示帮助
- `/customrecipes reload` - 重新加载配方

## 📋 技术说明

### 为什么这个版本更稳定？

1. **直接API调用**
   ```java
   ShapedRecipe recipe = new ShapedRecipe(lifeFruit);
   recipe.shape("DDD", "DGD", "EEE");
   recipe.setIngredient('D', Material.DIAMOND);
   Bukkit.addRecipe(recipe);
   ```

2. **避免配置解析错误** - 不依赖YAML配置文件的复杂解析

3. **简化事件处理** - 只使用Bukkit原生的配方系统

4. **确保兼容性** - 使用1.8.8完全支持的API

## 🎯 最终效果

现在的插件应该提供**完全原版的合成体验**：
- 像合成钻石剑一样自然
- 没有任何闪烁或异常
- 材料正常消耗
- 结果物品有自定义属性

## 📞 如果还有问题

如果这个版本仍然有问题，请提供：
1. 服务器完整启动日志
2. 合成时的具体表现（录屏更好）
3. 其他已安装的插件列表

---

**这个版本应该完全解决之前的所有问题！** 🎉
