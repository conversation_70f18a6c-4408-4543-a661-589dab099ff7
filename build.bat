@echo off
echo 正在编译 CustomRecipes 插件...
echo.

REM 检查Maven是否安装
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven，请先安装Maven并添加到PATH环境变量中
    pause
    exit /b 1
)

REM 清理并编译
echo 开始编译...
mvn clean package

if %errorlevel% equ 0 (
    echo.
    echo ================================
    echo 编译成功！
    echo 插件文件位置: target\CustomRecipes-1.0.0.jar
    echo ================================
    echo.
    echo 使用方法:
    echo 1. 将 target\CustomRecipes-1.0.0.jar 复制到服务器的 plugins 文件夹
    echo 2. 重启服务器或使用 /reload 命令
    echo 3. 使用 /customrecipes 命令查看帮助
    echo.
) else (
    echo.
    echo ================================
    echo 编译失败！请检查错误信息
    echo ================================
)

pause
