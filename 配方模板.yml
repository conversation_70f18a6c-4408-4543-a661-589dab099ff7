# 配方模板文件
# 复制以下模板到 config.yml 的 recipes 节点下

# ========== 有序合成模板 ==========
# 模板名称：有序合成配方
template_shaped_recipe:
  name: "配方显示名称"
  result:
    material: MATERIAL_NAME  # 结果物品材料
    amount: 1               # 数量
    data: 0                 # 数据值（可选）
  shape:
    - "ABC"  # 第一行
    - "DEF"  # 第二行  
    - "GHI"  # 第三行
  ingredients:
    A: MATERIAL_1  # 字母对应的材料
    B: MATERIAL_2
    C: MATERIAL_3
    # ... 继续添加需要的材料
  shaped: true

# ========== 无序合成模板 ==========
# 模板名称：无序合成配方
template_shapeless_recipe:
  name: "配方显示名称"
  result:
    material: MATERIAL_NAME  # 结果物品材料
    amount: 1               # 数量
    data: 0                 # 数据值（可选）
  ingredients:
    MATERIAL_1: 2  # 材料名称: 数量
    MATERIAL_2: 1
    MATERIAL_3: 4
    # ... 继续添加需要的材料
  shaped: false

# ========== 实用配方示例 ==========

# 示例1：下界之星配方
nether_star_recipe:
  name: "下界之星"
  result:
    material: NETHER_STAR
    amount: 1
  shape:
    - "DDD"
    - "DWD"
    - "DDD"
  ingredients:
    D: DIAMOND
    W: WITHER_SKELETON_SKULL
  shaped: true

# 示例2：经验瓶配方
experience_bottle_recipe:
  name: "经验瓶"
  result:
    material: EXP_BOTTLE
    amount: 3
  shape:
    - " G "
    - "GEG"
    - " G "
  ingredients:
    G: GLASS
    E: EMERALD
  shaped: true

# 示例3：鞘翅配方（如果你想让鞘翅可合成）
elytra_recipe:
  name: "鞘翅"
  result:
    material: ELYTRA
    amount: 1
  shape:
    - "LDL"
    - "DND"
    - "L L"
  ingredients:
    L: LEATHER
    D: DRAGON_EGG
    N: NETHER_STAR
  shaped: true

# 示例4：附魔书配方
enchanted_book_recipe:
  name: "附魔书"
  result:
    material: ENCHANTED_BOOK
    amount: 1
  ingredients:
    BOOK: 1
    DIAMOND: 4
    EMERALD: 2
  shaped: false

# 示例5：信标配方
beacon_recipe:
  name: "信标"
  result:
    material: BEACON
    amount: 1
  shape:
    - "GGG"
    - "GSG"
    - "OOO"
  ingredients:
    G: GLASS
    S: NETHER_STAR
    O: OBSIDIAN
  shaped: true

# ========== 常用材料参考 ==========
# 
# 基础材料：
# STONE, COBBLESTONE, DIRT, GRASS
# WOOD, LOG, PLANKS, STICK
# IRON_INGOT, GOLD_INGOT, DIAMOND, EMERALD
# COAL, CHARCOAL, REDSTONE, LAPIS_LAZULI
#
# 食物：
# APPLE, BREAD, COOKED_BEEF, COOKED_PORKCHOP
# GOLDEN_APPLE, CAKE, COOKIE
#
# 工具武器：
# DIAMOND_SWORD, IRON_PICKAXE, GOLDEN_AXE
# BOW, ARROW, SHIELD
#
# 方块：
# GOLD_BLOCK, IRON_BLOCK, DIAMOND_BLOCK
# TNT, OBSIDIAN, BEDROCK
#
# 特殊物品：
# ENDER_PEARL, BLAZE_ROD, GHAST_TEAR
# NETHER_STAR, DRAGON_EGG, ELYTRA
# ENCHANTED_BOOK, EXP_BOTTLE
#
# 注意：材料名称必须与 Bukkit Material 枚举完全匹配
