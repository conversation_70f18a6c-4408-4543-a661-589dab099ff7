package com.customrecipes;

import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.CraftItemEvent;
import org.bukkit.event.inventory.PrepareItemCraftEvent;
import org.bukkit.inventory.CraftingInventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * 合成事件监听器
 * 处理自定义配方的合成逻辑
 */
public class CraftingListener implements Listener {
    
    private final CustomRecipePlugin plugin;
    
    public CraftingListener(CustomRecipePlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPrepareCraft(PrepareItemCraftEvent event) {
        CraftingInventory inventory = event.getInventory();
        ItemStack[] matrix = inventory.getMatrix();
        
        // 检查是否是生命果实配方
        if (isLifeFruitRecipe(matrix)) {
            // 设置合成结果
            ItemStack result = createLifeFruit();
            inventory.setResult(result);
        }
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onCraftItem(CraftItemEvent event) {
        CraftingInventory inventory = event.getInventory();
        ItemStack[] matrix = inventory.getMatrix();
        
        // 检查是否是生命果实配方
        if (isLifeFruitRecipe(matrix)) {
            // 确保结果正确
            ItemStack result = createLifeFruit();
            inventory.setResult(result);
        }
    }
    
    /**
     * 检查是否是生命果实配方
     */
    private boolean isLifeFruitRecipe(ItemStack[] matrix) {
        // 检查3x3网格
        if (matrix.length != 9) return false;
        
        // 期望的配方：
        // [D] [D] [D]
        // [D] [G] [D]
        // [E] [E] [E]
        
        // 检查每个位置
        return checkMaterial(matrix[0], Material.DIAMOND) &&  // 位置0: 钻石
               checkMaterial(matrix[1], Material.DIAMOND) &&  // 位置1: 钻石
               checkMaterial(matrix[2], Material.DIAMOND) &&  // 位置2: 钻石
               checkMaterial(matrix[3], Material.DIAMOND) &&  // 位置3: 钻石
               checkMaterial(matrix[4], Material.GOLDEN_APPLE) && // 位置4: 金苹果
               checkMaterial(matrix[5], Material.DIAMOND) &&  // 位置5: 钻石
               checkMaterial(matrix[6], Material.EMERALD) &&  // 位置6: 绿宝石
               checkMaterial(matrix[7], Material.EMERALD) &&  // 位置7: 绿宝石
               checkMaterial(matrix[8], Material.EMERALD);    // 位置8: 绿宝石
    }
    
    /**
     * 检查物品材料
     */
    private boolean checkMaterial(ItemStack item, Material expectedMaterial) {
        if (item == null || item.getType() == Material.AIR) {
            return false;
        }
        return item.getType() == expectedMaterial;
    }
    
    /**
     * 创建生命果实
     */
    private ItemStack createLifeFruit() {
        // 创建附魔金苹果
        ItemStack item = new ItemStack(Material.GOLDEN_APPLE, 1, (short) 1);
        
        try {
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                // 设置名字
                meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', "&6&l生命果实"));
                
                // 设置lore
                List<String> lore = new ArrayList<>();
                lore.add(ChatColor.translateAlternateColorCodes('&', "&7一颗蕴含着强大生命力的神秘果实"));
                lore.add(ChatColor.translateAlternateColorCodes('&', "&7食用后可以获得强大的生命恢复能力"));
                lore.add("");
                lore.add(ChatColor.translateAlternateColorCodes('&', "&e&l稀有度: &6传说"));
                lore.add(ChatColor.translateAlternateColorCodes('&', "&e&l效果: &a生命恢复 + 伤害吸收"));
                meta.setLore(lore);
                
                item.setItemMeta(meta);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("创建生命果实时出错: " + e.getMessage());
        }
        
        return item;
    }
}
