package com.customrecipes;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.Recipe;
import org.bukkit.inventory.ShapedRecipe;
import org.bukkit.inventory.ShapelessRecipe;
import org.bukkit.material.MaterialData;

import java.util.*;

/**
 * 配方管理器
 * Recipe Manager
 */
public class RecipeManager {
    
    private final CustomRecipePlugin plugin;
    private final Set<Recipe> customRecipes;
    
    public RecipeManager(CustomRecipePlugin plugin) {
        this.plugin = plugin;
        this.customRecipes = new HashSet<>();
    }
    
    /**
     * 从配置文件加载配方
     */
    public void loadRecipesFromConfig() {
        ConfigurationSection recipesSection = plugin.getConfigManager().getRecipesSection();
        if (recipesSection == null) {
            plugin.getLogger().warning("配置文件中没有找到 recipes 节！");
            return;
        }
        
        int loadedCount = 0;
        for (String recipeKey : recipesSection.getKeys(false)) {
            ConfigurationSection recipeSection = recipesSection.getConfigurationSection(recipeKey);
            if (recipeSection == null) continue;
            
            try {
                Recipe recipe = createRecipeFromConfig(recipeKey, recipeSection);
                if (recipe != null) {
                    Bukkit.addRecipe(recipe);
                    customRecipes.add(recipe);
                    loadedCount++;
                    plugin.getLogger().info("已加载配方: " + recipeSection.getString("name", recipeKey));
                }
            } catch (Exception e) {
                plugin.getLogger().warning("加载配方 " + recipeKey + " 时出错: " + e.getMessage());
            }
        }
        
        plugin.getLogger().info("成功加载了 " + loadedCount + " 个自定义配方");
    }
    
    /**
     * 从配置创建配方
     */
    private Recipe createRecipeFromConfig(String key, ConfigurationSection config) {
        // 获取结果物品
        ItemStack result = createItemFromConfig(config.getConfigurationSection("result"));
        if (result == null) {
            plugin.getLogger().warning("配方 " + key + " 的结果物品无效");
            return null;
        }
        
        boolean shaped = config.getBoolean("shaped", true);
        
        if (shaped) {
            return createShapedRecipe(key, result, config);
        } else {
            return createShapelessRecipe(key, result, config);
        }
    }
    
    /**
     * 创建有序配方
     */
    private ShapedRecipe createShapedRecipe(String key, ItemStack result, ConfigurationSection config) {
        ShapedRecipe recipe = new ShapedRecipe(result);
        
        // 获取形状
        List<String> shape = config.getStringList("shape");
        if (shape.isEmpty()) {
            plugin.getLogger().warning("配方 " + key + " 缺少形状定义");
            return null;
        }
        
        // 设置形状
        recipe.shape(shape.toArray(new String[0]));
        
        // 获取材料映射
        ConfigurationSection ingredients = config.getConfigurationSection("ingredients");
        if (ingredients == null) {
            plugin.getLogger().warning("配方 " + key + " 缺少材料定义");
            return null;
        }
        
        // 设置材料
        for (String ingredientKey : ingredients.getKeys(false)) {
            String materialName = ingredients.getString(ingredientKey);
            Material material = Material.getMaterial(materialName);
            if (material != null) {
                recipe.setIngredient(ingredientKey.charAt(0), material);
            } else {
                plugin.getLogger().warning("配方 " + key + " 中的材料 " + materialName + " 无效");
            }
        }
        
        return recipe;
    }
    
    /**
     * 创建无序配方
     */
    private ShapelessRecipe createShapelessRecipe(String key, ItemStack result, ConfigurationSection config) {
        ShapelessRecipe recipe = new ShapelessRecipe(result);
        
        // 获取材料
        ConfigurationSection ingredients = config.getConfigurationSection("ingredients");
        if (ingredients == null) {
            plugin.getLogger().warning("配方 " + key + " 缺少材料定义");
            return null;
        }
        
        // 添加材料
        for (String materialName : ingredients.getKeys(false)) {
            Material material = Material.getMaterial(materialName);
            if (material != null) {
                int amount = ingredients.getInt(materialName, 1);
                for (int i = 0; i < amount; i++) {
                    recipe.addIngredient(material);
                }
            } else {
                plugin.getLogger().warning("配方 " + key + " 中的材料 " + materialName + " 无效");
            }
        }
        
        return recipe;
    }
    
    /**
     * 从配置创建物品
     */
    private ItemStack createItemFromConfig(ConfigurationSection config) {
        if (config == null) return null;
        
        String materialName = config.getString("material");
        if (materialName == null) return null;
        
        Material material = Material.getMaterial(materialName);
        if (material == null) return null;
        
        int amount = config.getInt("amount", 1);
        short data = (short) config.getInt("data", 0);
        
        ItemStack item = new ItemStack(material, amount, data);
        return item;
    }
    
    /**
     * 移除所有自定义配方
     */
    public void removeAllRecipes() {
        Iterator<Recipe> iterator = Bukkit.recipeIterator();
        while (iterator.hasNext()) {
            Recipe recipe = iterator.next();
            if (customRecipes.contains(recipe)) {
                iterator.remove();
            }
        }
        customRecipes.clear();
    }
    
    /**
     * 获取配方数量
     */
    public int getRecipeCount() {
        return customRecipes.size();
    }
}
